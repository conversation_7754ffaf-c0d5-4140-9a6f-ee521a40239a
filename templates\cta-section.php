<?php
/**
 * CTA Section Template
 * Reusable call-to-action section for all pages
 * 
 * Variables expected:
 * $cta_data - Array containing:
 *   - caption: Section caption text
 *   - title: Main CTA title
 *   - description: CTA description text
 *   - button_text: Button text
 *   - button_url: Button URL
 *   - background_class: Optional CSS class for background styling
 */

// Default values
$cta_data = $cta_data ?? [
    'caption' => 'Ready to Build?',
    'title' => 'Let\'s Create Something Amazing Together',
    'description' => 'Ready to bring your vision to life? Our team of experienced professionals is here to guide you through every step of your construction journey.',
    'button_text' => 'START YOUR PROJECT',
    'button_url' => 'contact.php',
    'background_class' => 'cta-gradient'
];
?>

<!-- CTA Section -->
<section class="cta-section section-padding <?php echo $cta_data['background_class'] ?? 'cta-gradient'; ?>">
    <div class="container">
        <div class="cta-content">
            <div class="cta-text">
                <span class="section-caption"><?php echo htmlspecialchars($cta_data['caption']); ?></span>
                <h2 class="section-title"><?php echo htmlspecialchars($cta_data['title']); ?></h2>
            </div>
            <p class="cta-description"><?php echo htmlspecialchars($cta_data['description']); ?></p>
            <a href="<?php echo htmlspecialchars($cta_data['button_url']); ?>" class="btn btn-primary">
                <?php echo htmlspecialchars($cta_data['button_text']); ?>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                </svg>
            </a>
        </div>
    </div>
</section>
